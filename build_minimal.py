#!/usr/bin/env python3
"""
最小化构建脚本，专门用于测试 httpx 问题
"""

import sys
import subprocess
import time  # 1. 导入 time 模块
from pathlib import Path


def build_minimal():
    """构建 minimal_test.py"""
    cmd = [
        sys.executable, "-m", "nuitka",

        "--main=minimal_test.py",
        "--output-filename=minimal_test.exe",
        "--output-dir=dist",

        "--standalone",
        "--assume-yes-for-downloads",

        "--follow-imports",

        "--windows-console-mode=attach",
        "--show-progress",
    ]

    print("🚀 开始构建 minimal_test.py...")

    # 2. 在执行命令前记录开始时间
    start_time = time.monotonic()

    # 执行构建命令
    subprocess.run(cmd, check=True)

    # 3. 在执行命令后记录结束时间
    end_time = time.monotonic()

    # 4. 计算并打印耗时
    duration = end_time - start_time
    print(f"🎉 构建完成: dist/minimal_test.exe")
    print(f"⏱️ 本次构建耗时: {duration:.2f} 秒")  # 使用 f-string 格式化输出为两位小数


def main():
    if not Path("minimal_test.py").exists():
        print("❌ 错误: 找不到 minimal_test.py")
        sys.exit(1)

    print("🧪 构建流程：主程序")


    build_minimal()


if __name__ == "__main__":
    main()